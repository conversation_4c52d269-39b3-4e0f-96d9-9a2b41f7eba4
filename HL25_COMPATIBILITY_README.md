# Half-Life 25th Anniversary Compatibility Guide

This document explains the changes made to RobinBot to ensure compatibility with the Half-Life 25th Anniversary update.

## What Changed in the 25th Anniversary Update

The Half-Life 25th Anniversary update introduced several significant changes that can break existing mods and hacks:

1. **Engine Limits Increased**: More entities, models, sounds, and textures are supported
2. **New Shader Support**: OpenGL renderer with shader support (`gl_use_shaders` cvar)
3. **Memory Layout Changes**: Engine structures and function addresses may have shifted
4. **Module Loading Changes**: How client.dll and hl.dll are loaded has changed
5. **New Features**: Steam Input, WebM support, dynamic shadows, etc.

## Compatibility Changes Made

### 1. Engine Version Detection
- Added automatic detection of 25th Anniversary engine using `gl_use_shaders` cvar
- Different behavior and error handling based on detected engine version

### 2. Improved Error Handling
- Added try-catch blocks around critical engine function calls
- More graceful handling of missing or changed engine interfaces
- Non-fatal warnings for optional features that may not work

### 3. Updated Memory Scanning
- Increased retry counts for pattern scanning
- Alternative search strings for finding engine functions
- Larger memory size estimates for new engine modules

### 4. Enhanced Module Detection
- Support for different module names (hw.dll, sw.dll, hl.exe)
- More lenient module requirements for 25th Anniversary
- Better fallback mechanisms when modules are not found

### 5. Updated Engine Limits
- Dynamic limits based on detected engine version
- Support for increased entity, model, sound, and texture limits
- Backward compatibility with legacy engine limits

## Installation Instructions

### For 25th Anniversary Engine:
1. Compile the updated RobinBot with the new compatibility code
2. Inject the DLL as usual
3. The bot will automatically detect the engine version and adapt

### For Legacy Engine:
1. The same compiled version works with legacy engines
2. Automatic fallback to legacy behavior when 25th Anniversary features are not detected

## Troubleshooting

### Common Issues:

1. **"Renderer info detection timed out"**
   - The engine may be too new or incompatible
   - Try running Half-Life in compatibility mode
   - Check if you're using a supported version

2. **"Studio interface not found" warning**
   - Some visual features may not work
   - The bot will continue to function with reduced capabilities
   - This is non-fatal and expected in some configurations

3. **Crashes on startup**
   - Ensure you're using the latest version of the bot
   - Try disabling antivirus software temporarily
   - Check if Half-Life is running in administrator mode

### Compatibility Matrix:

| Half-Life Version | Status | Notes |
|------------------|--------|-------|
| Legacy (pre-2023) | ✅ Full Support | All features work |
| 25th Anniversary | ✅ Compatible | Auto-detection, some features may be limited |
| Steam Legacy Branch | ✅ Full Support | Use `steam_legacy` beta branch if needed |

## Technical Details

### New Files Added:
- `HL25Compatibility.h` - Compatibility constants and macros
- `HL25_COMPATIBILITY_README.md` - This documentation

### Modified Files:
- `Client.cpp` - Added version detection and error handling
- `AutoOffset.cpp` - Improved pattern scanning and module detection
- `SetupHooks.cpp` - Enhanced initialization with better error handling

### Key Functions:
- `DetectEngineVersion()` - Automatically detects engine version
- `HL25_TRY_CATCH()` - Macro for safe exception handling
- `HL25_SAFE_CALL()` - Macro for safe function pointer calls

## Future Updates

This compatibility layer is designed to be forward-compatible with future Half-Life updates. The automatic detection system will adapt to new engine versions as they are released.

## Support

If you encounter issues with the 25th Anniversary compatibility:

1. Check that you're using the latest version of RobinBot
2. Verify your Half-Life installation is up to date
3. Try the `steam_legacy` beta branch if problems persist
4. Report issues with detailed error messages and system information

## Developer Notes

When adding new features, use the compatibility macros and check `g_bIsHL25Anniversary` to ensure your code works with both engine versions.
