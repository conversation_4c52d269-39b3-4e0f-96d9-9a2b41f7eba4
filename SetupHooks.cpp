#include "SetupHooks.h"
#include "Command.h"
#include "Client.h"
#include "detours.h"
#include "HL25Compatibility.h"

int SetupHooks::AddCommand(char *cmd_name,void(*function)(void))
{
	return 0;
}

cvar_t* SetupHooks::RegisterVariable (char *szName,char *szValue, int flags)
{
	cvar_t* pResult = g_Engine.pfnGetCvarPointer(szName);
	if(pResult != NULL)
		return pResult;
	return g_Engine.pfnRegisterVariable(szName, szValue, flags);
}

DWORD SetupHooks::StartFunc(LPSTARTUP_PARAM pStartup)
{
    SetupHooks* pClass        = pStartup->pClass;
    LPTHREAD_METHOD pMethod = pStartup->pMethod;
    LPVOID pParam            = pStartup->pParam;
    DWORD dwResult = (pClass->*pMethod)(pParam);
    delete pStartup;
    return dwResult;
}

void SetupHooks::StartThread(LPTHREAD_METHOD pMethod, LPVOID pParam,
                             LPDWORD pdwThreadID /* = NULL */,
                             LPSECURITY_ATTRIBUTES pSecurity /* = NULL */,
                             DWORD dwStackSize /* = 0 */,
                             DWORD dwFlags /* = 0 */)
{
    LPSTARTUP_PARAM pStartup = new STARTUP_PARAM;
    pStartup->pClass    = this;
    pStartup->pMethod    = pMethod;
    pStartup->pParam    = pParam;
    CreateThread(pSecurity, dwStackSize, (LPTHREAD_START_ROUTINE)StartFunc, pStartup, dwFlags, pdwThreadID);
}

DWORD SetupHooks::Initialize(LPVOID pParam)
{
	Client*	pClient = new Client;
	AutoOffset* Offset = new AutoOffset;

	// Increased timeout for 25th Anniversary compatibility
	int rendererRetries = 0;
	const int maxRendererRetries = 50; // Increased from implicit infinite loop

	while ( !Offset->GetRendererInfo() && rendererRetries < maxRendererRetries )
	{
		Sleep(90);
		rendererRetries++;
	}

	if (rendererRetries >= maxRendererRetries) {
		Offset->Error("Renderer info detection timed out - engine may not be compatible");
		return 1;
	}

	// Increased retry count for 25th Anniversary
	const int maxRetries = 20; // Increased from 10
	for (int i = 0; i < maxRetries; i++)
	{
		try {
			g_pClient = (cl_clientfunc_t*)Offset->ClientFuncs();
			g_pEngine = (cl_enginefunc_t*)Offset->EngineFuncs();
			if(g_pClient && g_pEngine)
				goto Return;
		}
		catch (...) {
			// Continue trying on exceptions
		}
		Sleep(100); // Small delay between retries
	}
	Offset->Error("Couldn't find default scanning pattern after multiple retries.");

Return:

	try {
		g_pStudio = (engine_studio_api_t*)Offset->EngineStudio();
	}
	catch (...) {
		// Studio interface might fail in some cases, continue anyway
		g_pStudio = nullptr;
	}

	Sleep(500);

	if(!g_pClient || !g_pEngine)
		Offset->Error("CRITICAL ERROR: Could not find client or engine functions");

	if (!g_pStudio) {
		// Non-fatal warning for studio interface
		MessageBoxA(0, "Warning: Studio interface not found. Some features may not work.", "RobinBot Warning", MB_OK | MB_ICONWARNING);
	}

	RtlCopyMemory(&g_Engine, g_pEngine, sizeof(cl_enginefunc_t));
	if (g_pStudio) {
		RtlCopyMemory(&g_Studio, g_pStudio, sizeof(engine_studio_api_t));
	}
	RtlCopyMemory(&g_Client, g_pClient, sizeof(cl_clientfunc_t));

	pClient->HookClient();

	Sleep(500);

	g_pEngine->pfnAddCommand = &AddCommand;
	g_pEngine->pfnRegisterVariable = &RegisterVariable;
	g_pEngine->pfnHookUserMsg = &pfnHookUserMsg;

	Command* pCmd = new Command;
	pcmd_t cmd = pCmd->CommandByName("cmd");
	if (cmd) {
		pCmd->EnDsCommand(cmd,false); // Fix - Can't "cmd", not connected
	}

	// Use appropriate interface version based on engine
	int interfaceVersion = g_bIsHL25Anniversary ? HL25_CLDLL_INTERFACE_VERSION : CLDLL_INTERFACE_VERSION;
	g_Client.Initialize(g_pEngine, interfaceVersion);
	g_Client.HUD_Init();

	if (cmd) {
		pCmd->EnDsCommand(cmd,true);
	}

	g_pEngine->pfnAddCommand = g_Engine.pfnAddCommand;
	g_pEngine->pfnRegisterVariable = g_Engine.pfnRegisterVariable;
	g_pEngine->pfnHookUserMsg = g_Engine.pfnHookUserMsg;

	Sleep(500);

	pClient->HookEngine();
	if (g_pStudio) {
		pClient->HookStudio();
	}

	delete pCmd;
	delete Offset;
	delete pClient;

	return 0;
}