#pragma once

// Half-Life 25th Anniversary Compatibility Header
// This file contains definitions and constants for compatibility with the 25th Anniversary update

#ifndef HL25_COMPATIBILITY_H
#define HL25_COMPATIBILITY_H

// Version detection
extern bool g_bIsHL25Anniversary;
extern bool g_bEngineVersionDetected;

// Engine version detection function
void DetectEngineVersion();

// Compatibility constants
#define HL25_CLDLL_INTERFACE_VERSION 7  // May need to be updated for 25th Anniversary
#define HL25_MAX_EDICTS 2048            // Increased from 900 in legacy
#define HL25_MAX_MODELS 1024            // Increased from 512 in legacy
#define HL25_MAX_SOUNDS 1024            // Increased from 512 in legacy
#define HL25_MAX_TEXTURES 4096          // Increased from 2048 in legacy

// Legacy constants for fallback
#define LEGACY_MAX_EDICTS 900
#define LEGACY_MAX_MODELS 512
#define LEGACY_MAX_SOUNDS 512
#define LEGACY_MAX_TEXTURES 2048

// Dynamic limits based on engine version
inline int GetMaxEdicts() {
    return g_bIsHL25Anniversary ? HL25_MAX_EDICTS : LEGACY_MAX_EDICTS;
}

inline int GetMaxModels() {
    return g_bIsHL25Anniversary ? HL25_MAX_MODELS : LEGACY_MAX_MODELS;
}

inline int GetMaxSounds() {
    return g_bIsHL25Anniversary ? HL25_MAX_SOUNDS : LEGACY_MAX_SOUNDS;
}

inline int GetMaxTextures() {
    return g_bIsHL25Anniversary ? HL25_MAX_TEXTURES : LEGACY_MAX_TEXTURES;
}

// Error handling macros for compatibility
#define HL25_TRY_CATCH(code, fallback) \
    try { \
        code; \
    } catch (...) { \
        if (g_bIsHL25Anniversary) { \
            fallback; \
        } else { \
            throw; \
        } \
    }

// Safe function pointer checking
#define HL25_SAFE_CALL(func, ...) \
    if (func) { \
        try { \
            func(__VA_ARGS__); \
        } catch (...) { \
            /* Ignore exceptions in 25th Anniversary mode */ \
        } \
    }

#endif // HL25_COMPATIBILITY_H
