#include "SvcMessage.h"
#include "AutoOffset.h"
#include "players.h"
#include "Command.h"
#include "drawing.h"
#include "Client.h"
#include "Main.h"
#include "gamemode.h"
#include <sstream>
#pragma warning(disable:4800)
#pragma warning(disable:4002)
#pragma warning(disable:4244)
#pragma warning(disable:4996)

// Forward declaration
void HlEngineCommand(const char* command);

player_s g_Player[33];
CPlayers g_cPlayers;
CommandInterpreter cmd;
AIMBOT_INFO g_sAimbot;

// Variables to track the last targeted player
int g_iLastTargetIndex = -1;
float g_fLastTargetTime = 0.0f;

// External variable to track if aimbot is activated via console command
extern bool g_bAimbotActivatedByCommand;

// External variable to track if perfect block is activated via console command
extern bool g_bPerfectBlockActivatedByCommand;

// External variables to track teleswoop state
extern bool g_bTeleswoopActive;
extern float g_fTeleswoopEndTime;

//Menu Variables
bool selectedItem1 = true;
bool selectedItem2 = false;
bool selectedItem3 = false;
bool selectedItem6 = false;
bool selectedItem7 = false;
bool selectedItem8 = false;
bool selectedItem9 = false;  // Add this for fast swoop
bool activeItem1 = true;
bool activeItem2 = true;
bool activeItem3 = true;
bool activeItem6 = true;
bool activeItem7 = true;
bool activeItem8 = false;
bool activeItem9 = false;  // Add this for fast swoop

int c_Player[33];
int c_PlayerDist[33];

bool bGameStart = false;

#define MIN_SMOOTH 0.000001f
#define CVAR(cvar_name) cvar.##cvar_name
#define MAX_TARGET_MEMORY_TIME 2.0f  // Maximum time in seconds to remember the last target
#define MAX_TARGET_MEMORY_DISTANCE 2000.0f  // Maximum distance in units to remember the last target

bool fastSwoopEnabled = false;
float lastVelocity = 0.0f;
bool swoopResetInProgress = false;
float forwardPressStartTime = 0.0f;
bool wasForwardPressed = false;
bool wasMoveleftPressed = false;
bool wasMoverightPressed = false;
bool wasInBounceMissile = false;
bool bounceMissileInterrupted = false;
float swoopResetStartTime = 0.0f; // Time when swoop reset started
bool swoopResetCommandPending = false; // Flag to indicate a pending command
float swoopResetCommandTime = 0.0f; // Time when to execute the pending command
bool targetCollisionDetected = false; // Flag to indicate if a collision with a target was detected
Vector lastPlayerPosition; // Last known player position for collision detection
float lastCollisionCheckTime = 0.0f; // Time of last collision check

void Client::StudioEntityLight(struct alight_s *plight)
{
	CPlayer *cPlayer = nullptr;
	add_log("StudioEntityLight start");
	if (bGameStart && g_Local.bConnected)
	{
		add_log("StudioEntityLight 1");
		cl_entity_s *pEntity = g_pStudio->GetCurrentEntity();
		add_log("StudioEntityLight 2");
		if (pEntity && pEntity->player && pEntity->index != g_cPlayers.Me()->Entity()->index)
		{
			add_log("StudioEntityLight 3");
			if ( CVAR(aim_target) > 20 ) CVAR(aim_target) = 20;
			add_log("StudioEntityLight 4");
			cPlayer = g_cPlayers.Player(pEntity->index);
			add_log("StudioEntityLight 5");
			cPlayer->GetBoneInformation(VM_HITBOX, CVAR(aim_target));
			add_log("StudioEntityLight 6");
			cPlayer->Info()->vAimBone.z += CVAR(aim_height);
			add_log("StudioEntityLight 7");
		}
	}
	add_log("StudioEntityLight end");
	return g_Studio.StudioEntityLight(plight);
}

void strreplace(char* buf, const char* search, const char* replace)
{
	char* p = buf;
	int l1 = strlen(search);
	int l2 = strlen(replace);
	while (p = strstr(p, search))
	{
		memmove(p + l2, p + l1, strlen(p + l1) + 1);
		memcpy(p, replace, l2);
		p += l2;
	}
}

void RunScript(char* scriptName)
{
	//Devre d��� b�rak�ld�.
}

void Client::InitHack(void)
{
	g_Screen.iSize = sizeof(SCREENINFO);
	g_Engine.pfnGetScreenInfo(&g_Screen);

	g_Engine.Con_Printf( "\n\t--->RobinBot has loaded.\n" );

	//HideModule(hInstance);
	//HideModuleXta(hInstance);
	//HideModuleFromPEB(hInstance);f
	//RemovePeHeader((DWORD)hInstance);
	//DestroyModuleHeader(hInstance);

	cmd.init();
	cvar.init();



	//RunScript("config.cfg");
	//string file = szDirFile("config.cfg");
	//cmd.execFile(file.c_str());
}

bool Client::IsInFOV(float *fScreen, float fFov)
{
	if ((fFov == 0.0f)								||
		((fScreen[0] <= g_Screen.iWidth/2 + fFov)	&&
		(fScreen[0] >= g_Screen.iWidth/2 - fFov)	&&
		(fScreen[1] <= g_Screen.iHeight/2 + fFov)	&&
		(fScreen[1] >= g_Screen.iHeight/2 - fFov)))
		return true;

	return false;
}

void Client::PredictEntity(cl_entity_s *pEntity, Vector *vOutput, unsigned int fAmount)
{
	net_status_s sNetwork;
	g_Engine.pNetAPI->Status(&sNetwork);
	int nHistory = (pEntity->current_position + HISTORY_MAX - fAmount) % HISTORY_MAX;
	Vector vOldOrigin, vCurOrigin, vDeltaOrigin;
	vOldOrigin = pEntity->ph[nHistory].origin;
	vCurOrigin = pEntity->ph[pEntity->current_position].origin;
	VectorSubtract(vCurOrigin, vOldOrigin, vDeltaOrigin);
	float fPing = (float)sNetwork.latency;
	if (fPing < 0.0f) fPing = -fPing;
	VectorScale(vDeltaOrigin, fPing, vDeltaOrigin);
	VectorAdd(pEntity->origin, vDeltaOrigin, *vOutput);
}

int Client::HUD_Reset(void)
{
	if (bGameStart)
	{
		for (int i = 1; i < 33; i++)
		{
			PPLAYER_INFO pInfo = g_cPlayers.Player(i)->Info();
			pInfo->bIsBoneScreen	= false;
			pInfo->bIsScreen		= false;
			pInfo->bIsVulnerable	= false;
			pInfo->bValid			= false;
			pInfo->sSound.bValid	= false;
			pInfo->sOldSound.bValid	= false;
		}
	}
	return g_Client.HUD_Reset();
}

void Client::HUD_Frame(double time)
{
	if ( Init )
	{
		AutoOffset* Offset = new AutoOffset;

		while ( !Offset->GetRendererInfo() )
			Sleep(90);

		Offset->SW = g_Studio.IsHardware();
		Offset->GameInfo();

		Offset->Find_SVCBase();
		Offset->Find_MSGInterface();
		Offset->Find_CBuf_AddText();
		Offset->Patch_CL_ConnectionlessPacket();

		CBuf_AddText_Orign = (HL_MSG_CBuf_AddText)Offset->pCBuf_AddText;

		SVC_StuffText_Orign = HookServerMsg(SVC_STUFFTEXT,SVC_StuffText,Offset);
		SVC_SendCvarValue_Orign = HookServerMsg(SVC_SENDCVARVALUE,SVC_SendCvarValue,Offset);
		SVC_SendCvarValue2_Orign = HookServerMsg(SVC_SENDCVARVALUE2,SVC_SendCvarValue2,Offset);

		Offset->SpeedPtr = (DWORD)Offset->SpeedHackPtr();

		InitHack();
		Init = false;

		delete Offset;
	}
	g_Engine.pNetAPI->Status(&(g_Local.net_status));
	g_Local.bConnected = (bool)g_Local.net_status.connected;
	g_Client.HUD_Frame(time);
}
void V_CalcRefdef(struct ref_params_s *pparams)
{
	if(GetAsyncKeyState(VK_NUMPAD1)) {
		pparams->health++;
	}
	VectorCopy(pparams->punchangle, g_Local.vPunchAngle);
	VectorCopy(pparams->viewangles, g_Local.vViewAngle);
	g_Local.iHP = pparams->health;
	g_Client.V_CalcRefdef(pparams);
}
// New function to draw ESP for players
void Client::DrawPlayerESP()
{
    CMe *cMe = g_cPlayers.Me();
    cMe->GetInfo();

    // Track valid players to avoid duplicates
    bool validPlayerIndices[33] = { false };

    for (int i = 1; i < 33; i++)
    {
        // Skip local player
        if (cMe->Entity()->index == i)
            continue;

        CPlayer *cPlayer = g_cPlayers.Player(i);
        cPlayer->GetInfo(i);

        // Skip invalid entities
        if (!cPlayer->Entity() || !cPlayer->Entity()->player)
            continue;

        // Skip dead players
        if (!g_Player[i].bAlive)
            continue;

        // Skip invalid players
        if (!cPlayer->Info()->bValid)
            continue;

        // Mark this player index as valid
        validPlayerIndices[i] = true;

        // Force update bone screen position
        float boneScreenPos[2];
        bool isOnScreen = cPlayer->BoneScreenPosition(boneScreenPos);

        // Skip drawing ESP for players that are off-screen
        if (!isOnScreen) {
            continue; // Skip drawing for off-screen players
        }

        // Calculate distance to player (in game units)
        float distanceInMeters = cMe->Distance(cPlayer->Position());
        float distanceInUnits = distanceInMeters * 22.0f; // Convert back to game units

        // Validate distance (check for NaN or extreme values)
        if (isnan(distanceInUnits) || distanceInUnits < 0 || distanceInUnits > 10000.0f) {
            continue; // Skip drawing for invalid distances
        }

        // Set color based on team and distance
        BYTE r = 0, g = 173, b = 255; // Default light blue for enemies

        // Check if player is an ally (same team as local player)
        // Use our global game mode detection
        extern bool IsFreeForAllMode();
        bool isAlly = false;

        // In free-for-all mode, everyone is an enemy
        if (!IsFreeForAllMode()) {
            // In team mode, only consider players allies if they're on the same team
            isAlly = (cMe->Info()->eTeam == cPlayer->Info()->eTeam && cMe->Info()->eTeam != TEAM_NONE);
        }

        // Debug team comparison (only for the first player to avoid spam)
        if (i == 1) {
            g_Engine.Con_Printf("ESP Team Check: My team: %d, Player %d team: %d, isAlly: %d\n",
                cMe->Info()->eTeam, i, cPlayer->Info()->eTeam, isAlly);
        }

        if (isAlly) {
            // Light Purple for allies
            r = 200; g = 100; b = 255;
        } else {
            // For enemies, use distance-based coloring
            if (distanceInUnits <= 500.0f) {
                // Green for enemies within 500 units
                r = 0; g = 255; b = 0;
            } else if (distanceInUnits <= 900.0f) {
                // Yellow for enemies within 900 units
                r = 255; g = 255; b = 0;
            }
        }

        // Draw ESP box if enabled
        if (activeItem1)
        {
            g_Drawing.DrawBox((int)boneScreenPos[0] - 6, (int)boneScreenPos[1] - 6, 12, 12, 1, r, g, b, 150);

            // Draw distance and team below the ESP box
            g_Drawing.DrawString((int)boneScreenPos[0] - 23, (int)boneScreenPos[1] + 10, r, g, b, "%.0f", distanceInUnits);

            // Display team with text label (Ally/Enemy)
            const char* teamLabel = isAlly ? "Ally" : "Enemy";
            g_Drawing.DrawString((int)boneScreenPos[0] - 23, (int)boneScreenPos[1] + 20, r, g, b, "%s", teamLabel);
        }

        // Draw player name if enabled
        if (activeItem2 && g_Player[i].Info.name)
        {
            // Draw player name
            g_Drawing.DrawString((int)boneScreenPos[0] - 23, (int)boneScreenPos[1] - 25, r, g, b, g_Player[i].Info.name);
        }
    }
}

void Client::FarAim()
{
	CMe *cMe = g_cPlayers.Me();
	cMe->GetInfo();
	ZeroMemory(&g_sAimbot.sTarget, sizeof(TARGET_INFO));

	// Track valid players to avoid duplicates
	bool validPlayerIndices[33] = { false };

	// First pass: collect potential targets
	struct PotentialTarget {
		CPlayer* player;
		float physicalDistance; // Actual distance in game units
		float cursorDistance;   // Distance from crosshair
		float screenPos[2];     // Screen position
		int playerIndex;        // Player index
		bool isVisible;         // Is the player visible
	};

	// Array to store potential targets
	PotentialTarget potentialTargets[33];
	int targetCount = 0;

	for (int i = 1; i < 33; i++)
	{
		// Skip local player or spectated player
		if (cMe->Entity()->index == i || cMe->Entity()->curstate.iuser2 == i)
			continue;

		// Skip already processed players
		if (validPlayerIndices[i])
			continue;

		CPlayer *cPlayer = g_cPlayers.Player(i);
		cPlayer->GetInfo(i);

		// Skip invalid entities
		if (!cPlayer->Entity() || !cPlayer->Entity()->player)
			continue;

		// Skip dead players
		if (!g_Player[i].bAlive)
			continue;

		// Mark this player as processed
		validPlayerIndices[i] = true;

		// Update player origin if valid
		if (cPlayer->Info()->bValid)
		{
			VectorCopy(cPlayer->Entity()->origin, cPlayer->Info()->vOrigin);
		}

		// Check visibility
		bool bIsVisible = cMe->Visible(cPlayer->Position());

		// Skip invalid and invisible players
		if (!cPlayer->Info()->bValid && !bIsVisible)
		{
			g_Local.trigger[i] = false;
			cPlayer->Info()->sSound.bValid = false;
			continue;
		}

		// Calculate screen position
		cPlayer->ScreenPosition(NULL);

		// Force calculation of bone position for valid players
		Vector *bonePos = nullptr;
		float boneScreen[2] = {0, 0};

		if (cPlayer->Info()->bValid) {
			// Update the bone screen position
			bonePos = cPlayer->BonePosition();
			bool isOnScreen = cPlayer->BoneScreenPosition(boneScreen);

			// Skip players that are off-screen
			if (!isOnScreen) {
				continue;
			}

			// Validate bone position
			if (isnan(bonePos->x) || isnan(bonePos->y) || isnan(bonePos->z) ||
				fabs(bonePos->x) > 10000.0f || fabs(bonePos->y) > 10000.0f || fabs(bonePos->z) > 10000.0f) {
				// Invalid bone position, skip this player
				continue;
			}
		}
		else {
			// Skip players without valid bone positions
			continue;
		}

		// Reset triggerbot state
		g_Local.trigger[i] = false;

		// Check if aimbot is active and player is valid
		if (CVAR(aim_active) && cPlayer->Info()->bValid)
		{
			// Check if player is an enemy and is visible
			// Use our global game mode detection
			extern bool IsFreeForAllMode();
			bool isEnemy = true;

			// In free-for-all mode, everyone is an enemy
			if (!IsFreeForAllMode()) {
				// In team mode, only consider players enemies if they're on a different team
				isEnemy = (cMe->Info()->eTeam != cPlayer->Info()->eTeam || cMe->Info()->eTeam == TEAM_NONE);
			}

			// Debug team comparison
			if (i == 1) { // Only log for player 1 to avoid console spam
				g_Engine.Con_Printf("Team comparison: My team: %d, Player %d team: %d, isEnemy: %d\n",
					cMe->Info()->eTeam, i, cPlayer->Info()->eTeam, isEnemy);
			}

			if (isEnemy && (CVAR(aim_autowall) || bIsVisible))
			{
				// Calculate distance to player in game units
				float fDistance = cMe->Distance(cPlayer->Position());
				float distanceInUnits = fDistance * 22.0f; // Convert to game units

				// Validate distance
				if (isnan(distanceInUnits) || distanceInUnits < 0 || distanceInUnits > 10000.0f) {
					continue; // Skip invalid distances
				}

				// Check if player is within range - increased to 2000 units and no FOV check
				bool isInRange = (distanceInUnits <= 2000.0f);

				// Always consider players within range, regardless of FOV
				if (isInRange)
				{
					// Calculate cursor distance (distance from crosshair)
					float fCursorDist = POW(g_Screen.iWidth / 2 - cPlayer->Info()->fBoneScreen[0]) +
						POW(g_Screen.iHeight / 2 - cPlayer->Info()->fBoneScreen[1]);

					// Add to potential targets
					potentialTargets[targetCount].player = cPlayer;
					potentialTargets[targetCount].physicalDistance = distanceInUnits;
					potentialTargets[targetCount].cursorDistance = fCursorDist;
					potentialTargets[targetCount].screenPos[0] = cPlayer->Info()->fBoneScreen[0];
					potentialTargets[targetCount].screenPos[1] = cPlayer->Info()->fBoneScreen[1];
					potentialTargets[targetCount].playerIndex = i;
					potentialTargets[targetCount].isVisible = bIsVisible;
					targetCount++;
				}
			}

			// Handle console command
			if (g_bAimbotActivatedByCommand && activeItem3) {
				// Use our global game mode detection
				extern bool IsFreeForAllMode();
				bool shouldTarget = true;

				// In free-for-all mode, target everyone
				if (!IsFreeForAllMode()) {
					// In team mode, only target enemies
					shouldTarget = (cPlayer->Info()->eTeam != cMe->Info()->eTeam || cMe->Info()->eTeam == TEAM_NONE);
				}

				if (shouldTarget && g_Player[i].bAlive) {
					g_Engine.pfnSetMousePos((int)cPlayer->Info()->fBoneScreen[0], (int)cPlayer->Info()->fBoneScreen[1]);
				}
			}
		}
	}

	// Second pass: select the best target based on priority logic
	if (targetCount > 0) {
		// Default best target is the first one
		int bestTargetIndex = 0;
		bool foundLastTarget = false;

		// Check if the last target is still valid and within time/distance limits
		if (g_iLastTargetIndex != -1 && (g_Engine.GetClientTime() - g_fLastTargetTime) < MAX_TARGET_MEMORY_TIME) {
			// Find the last target in our potential targets
			for (int i = 0; i < targetCount; i++) {
				if (potentialTargets[i].playerIndex == g_iLastTargetIndex) {
					// If the last target is within the maximum distance, prioritize it
					if (potentialTargets[i].physicalDistance <= MAX_TARGET_MEMORY_DISTANCE) {
						foundLastTarget = true;
						bestTargetIndex = i;
					}
					break;
				}
			}
		}

		// If we didn't find the last target or it's too far away, use different selection methods
		if (!foundLastTarget) {
			// First prioritize visible targets
			bool foundVisibleTarget = false;
			float bestVisibleCursorDist = FLT_MAX;

			for (int i = 0; i < targetCount; i++) {
				if (potentialTargets[i].isVisible) {
					if (!foundVisibleTarget || potentialTargets[i].cursorDistance < bestVisibleCursorDist) {
						foundVisibleTarget = true;
						bestVisibleCursorDist = potentialTargets[i].cursorDistance;
						bestTargetIndex = i;
					}
				}
			}

			// If no visible targets, use closest to crosshair
			if (!foundVisibleTarget && targetCount > 0) {
				float bestCursorDist = potentialTargets[0].cursorDistance;

				for (int i = 1; i < targetCount; i++) {
					if (potentialTargets[i].cursorDistance < bestCursorDist) {
						bestCursorDist = potentialTargets[i].cursorDistance;
						bestTargetIndex = i;
					}
				}
			}
		}

		// Set the best target
		PTARGET_INFO pTarget = &(g_sAimbot.sTarget);
		pTarget->cPlayer = potentialTargets[bestTargetIndex].player;
		pTarget->fDistance = potentialTargets[bestTargetIndex].cursorDistance;
		pTarget->fScreen[0] = potentialTargets[bestTargetIndex].screenPos[0];
		pTarget->fScreen[1] = potentialTargets[bestTargetIndex].screenPos[1];

		// Store the selected target index for next frame
		g_iLastTargetIndex = potentialTargets[bestTargetIndex].playerIndex;
	}

	// Apply aimbot if we have a valid target
	if (g_sAimbot.sTarget.cPlayer != NULL && !cMe->Info()->bIsReloading && !cMe->IsFlashed())
	{
		Vector vAimOrigin;

		// Get bone position for aiming
		Vector *targetBonePos = g_sAimbot.sTarget.cPlayer->BonePosition();

		// Validate bone position
		if (targetBonePos && !isnan(targetBonePos->x) && !isnan(targetBonePos->y) && !isnan(targetBonePos->z) &&
			fabs(targetBonePos->x) < 10000.0f && fabs(targetBonePos->y) < 10000.0f && fabs(targetBonePos->z) < 10000.0f) {

			VectorCopy((*targetBonePos), vAimOrigin);

			// Apply prediction if enabled
			if (CVAR(aim_prediction))
			{
				Vector vDelta;
				cl_entity_s *pEntity = g_sAimbot.sTarget.cPlayer->Entity();

				if (pEntity) {
					VectorSubtract(vAimOrigin, pEntity->origin, vDelta);
					PredictEntity(pEntity, &vAimOrigin, (int)CVAR(aim_prediction));
					VectorAdd(vAimOrigin, vDelta, vAimOrigin);
				}
			}

			// Calculate view angles to target
			cMe->CalcViewAngles(&vAimOrigin, NULL);
			g_sAimbot.bActive = true;
		}
		else {
			// Invalid bone position
			g_sAimbot.bActive = false;
		}
	}
	else
	{
		g_sAimbot.bActive = false;
	}
}



// Debug function to check server cvars
void DebugServerCvars()
{
    static float lastCheckTime = 0;
    float currentTime = g_Engine.GetClientTime();

    // Only check every 5 seconds to avoid spam
    if (currentTime - lastCheckTime < 5.0f)
        return;

    lastCheckTime = currentTime;

    // Check mp_gamemode
    cvar_t* mp_gamemode = g_Engine.pfnGetCvarPointer("mp_gamemode");
    if (mp_gamemode) {
        g_Engine.Con_Printf("DEBUG: mp_gamemode exists with value: %.1f\n", mp_gamemode->value);
    } else {
        g_Engine.Con_Printf("DEBUG: mp_gamemode cvar not found\n");
    }

    // Check sv_gamemode (the old incorrect cvar)
    cvar_t* sv_gamemode = g_Engine.pfnGetCvarPointer("sv_gamemode");
    if (sv_gamemode) {
        g_Engine.Con_Printf("DEBUG: sv_gamemode exists with value: %.1f\n", sv_gamemode->value);
    } else {
        g_Engine.Con_Printf("DEBUG: sv_gamemode cvar not found\n");
    }

    // Check other possible gamemode cvars
    const char* possibleCvars[] = {"gamemode", "game_mode", "g_gamemode", "mp_teamplay"};
    for (int i = 0; i < 4; i++) {
        cvar_t* cvar = g_Engine.pfnGetCvarPointer(possibleCvars[i]);
        if (cvar) {
            g_Engine.Con_Printf("DEBUG: %s exists with value: %.1f\n", possibleCvars[i], cvar->value);
        }
    }

    // Log current team assignment
    g_Engine.Con_Printf("DEBUG: Current local player team: %d\n", g_Local.iTeam);
}

void Client::HUD_Redraw(float time, int intermission)
{
	g_Client.HUD_Redraw(time, intermission);

	if (!bGameStart)
	{
		HUD_Reset();
		bGameStart = true;
	}

	cl_entity_t *pLocal = g_Engine.GetLocalPlayer();
	g_Local.iIndex = pLocal->index;

	for (int i = 1; i < 33; i++)
	{
		cl_entity_s *ent = g_Engine.GetEntityByIndex(i);
		g_Engine.pfnGetPlayerInfo(i, &g_Player[i].Info);
		g_Player[i].bAlive = ent && !(ent->curstate.effects & EF_NODRAW) && ent->player && ent->curstate.movetype != 6 && ent->curstate.movetype != 0;
		g_Player[i].vOrigin = ent->origin;
		g_Player[i].Angles = ent->angles;
	}

	if (g_Local.bConnected) {
		// Debug server cvars to help diagnose free-for-all detection issues
		DebugServerCvars();

		// Detect game mode periodically
		static float lastGameModeCheck = 0;
		if (time - lastGameModeCheck > 5.0f) {
			// External function to detect game mode
			extern void DetectGameMode();
			DetectGameMode();
			lastGameModeCheck = time;
		}

		// Update the last target time if we have a valid target
		if (g_sAimbot.sTarget.cPlayer != NULL && g_sAimbot.bActive) {
			// The player index is already stored in g_iLastTargetIndex in FarAim()
			g_fLastTargetTime = time;
		}

		// Run aimbot logic
		FarAim();

		// Draw ESP for players
		DrawPlayerESP();

		// Draw hack menu
		HackMenu();
	}
}

void Client::HUD_PlayerMove(struct playermove_s *ppmove, int server)
{
	g_Client.HUD_PlayerMove( ppmove, server );

	g_Engine.pEventAPI->EV_LocalPlayerViewheight(g_Local.vEye);
	g_Local.vOrigin = ppmove->origin;
	g_Local.vEye = g_Local.vEye + ppmove->origin;

	// Store velocity and base velocity for goldsrc-monitor compatibility
	g_Local.vVelocity = ppmove->velocity;
	g_Local.vBaseVelocity = ppmove->basevelocity;

	g_Local.iFlags = ppmove->flags;
	g_Local.iWaterLevel = ppmove->waterlevel;

	g_Local.fOnLadder = ppmove->movetype == 5;
	g_Local.iUseHull=ppmove->usehull;

	Vector vTemp1 = g_Local.vOrigin;
	vTemp1[2] -= 8192;
	pmtrace_t *trace = g_Engine.PM_TraceLine(g_Local.vOrigin, vTemp1, 1, ppmove->usehull, -1);

	g_Local.flHeight=abs(trace->endpos.z - g_Local.vOrigin.z);

	if(g_Local.flHeight <= 60) g_Local.flGroundAngle=acos(trace->plane.normal[2])/M_PI*180;
	else g_Local.flGroundAngle = 0;

	Vector vTemp2=trace->endpos;
	pmtrace_t pTrace;

	g_Engine.pEventAPI->EV_SetTraceHull( ppmove->usehull );
	g_Engine.pEventAPI->EV_PlayerTrace( g_Local.vOrigin, vTemp2, PM_GLASS_IGNORE | PM_STUDIO_BOX, g_Local.iIndex, &pTrace );

	if( pTrace.fraction < 1.0f )
	{
		g_Local.flHeight=abs(pTrace.endpos.z - g_Local.vOrigin.z);
		int ind=g_Engine.pEventAPI->EV_IndexFromTrace(&pTrace);
		if(ind>0&&ind<33)
		{
			float dst=g_Local.vOrigin.z-(g_Local.iUseHull==0?32:18)-g_Player[ind].vOrigin.z-g_Local.flHeight;
			if(dst<30)
			{
				g_Local.flHeight-=14.0;
			}
		}
	}
}

void Client::SmoothAimAngles(Vector *vStart, Vector *vTarget, Vector *vOutput, float fSmoothness)
{
	if(fSmoothness == MIN_SMOOTH)
		return;
	Vector vDelta;
	VectorSubtract((*vTarget), (*vStart), vDelta);
	if (vDelta[1] > 180.0f) vDelta[1] -= 360.0f;
	if (vDelta[1] < -180.0f) vDelta[1] += 360.0f;
	vDelta[0] = vDelta[0] / fSmoothness;
	vDelta[1] = vDelta[1] / fSmoothness;
	VectorAdd((*vStart), vDelta, (*vOutput));
}

void Client::Bhop(float frametime, struct usercmd_s *cmd)
{
    // If bunny hop is disabled in menu, return early
    if (!activeItem7) {
        return;
    }

    static bool lastFramePressedJump=false;
    static bool JumpInNextFrame=false;
    int curFramePressedJump=cmd->buttons&IN_JUMP;
    if(JumpInNextFrame)
    {
        JumpInNextFrame=false;
        cmd->buttons|=IN_JUMP;
        goto bhopfuncend;
    }
    static int inAirBhopCnt=0;bool isJumped=false;

    if (curFramePressedJump)
    {
        cmd->buttons &= ~IN_JUMP;
        if( ((!lastFramePressedJump)|| g_Local.iFlags&FL_ONGROUND || g_Local.iWaterLevel >= 2 || g_Local.fOnLadder==1 || g_Local.flHeight<=2))
        {
            if(true)
            {
                static int bhop_jump_number=0;
                bhop_jump_number++;
                if(bhop_jump_number>=2)
                {
                    bhop_jump_number=0;
                    JumpInNextFrame=true;
                    goto bhopfuncend;
                }
            }
            {
                inAirBhopCnt=4;isJumped=true;
                cmd->buttons |= IN_JUMP;
            }
        }
    }
    if(!isJumped)
    {
        if(inAirBhopCnt>0)
        {
            if(inAirBhopCnt%2==0) {cmd->buttons |= IN_JUMP;}
            else cmd->buttons &= ~IN_JUMP;
            inAirBhopCnt--;
        }
    }
bhopfuncend:
    lastFramePressedJump=curFramePressedJump;
}

void Client::StrafeHack(float frametime, struct usercmd_s *cmd)
{
    // If strafe hack is disabled in menu, return early
    if (!activeItem8) {
        return;
    }

    // Get local player velocity (using goldsrc-monitor calculation)
    Vector combinedVel;
    combinedVel[0] = g_Local.vVelocity[0] + g_Local.vBaseVelocity[0];
    combinedVel[1] = g_Local.vVelocity[1] + g_Local.vBaseVelocity[1];
    float velocity = sqrt(POW(combinedVel[0]) + POW(combinedVel[1]));

    // Check if player is on the ground
    bool onGround = (g_Local.iFlags & FL_ONGROUND) != 0;

    // Static variables for tracking between function calls
    static float lastYaw = 0.0f;

    // Only apply strafe hack when in the air (not on ground)
    if (!onGround) {
        // Get the current view angles
        Vector viewAngles;
        g_Engine.GetViewAngles(viewAngles);

        // Track mouse movement for strafing
        float yawDelta = viewAngles[1] - lastYaw;

        // Normalize yaw delta to -180/180 range
        while (yawDelta > 180) yawDelta -= 360;
        while (yawDelta < -180) yawDelta += 360;

        // Store current yaw for next frame
        lastYaw = viewAngles[1];

        // If mouse is moving significantly, strafe in that direction
        if (fabs(yawDelta) > 0.1f) {
            if (yawDelta > 0) {
                // Mouse moving right - strafe right
                cmd->sidemove = 400.0f;
                cmd->forwardmove = 0.0f;
            } else {
                // Mouse moving left - strafe left
                cmd->sidemove = -400.0f;
                cmd->forwardmove = 0.0f;
            }
        } else {
            // Auto-strafe when mouse isn't moving much
            static bool strafeLeft = false;
            static float lastSwitchTime = 0.0f;
            float currentTime = g_Engine.GetClientTime();

            // Calculate optimal switch timing based on velocity
            float switchInterval = 0.15f; // Base timing

            // Switch direction based on timing
            if (currentTime - lastSwitchTime > switchInterval) {
                strafeLeft = !strafeLeft;
                lastSwitchTime = currentTime;
            }

            // Apply strafing without forward movement
            if (strafeLeft) {
                cmd->sidemove = -400.0f;
                cmd->viewangles[1] += 1.5f; // Smaller angle for more control
            } else {
                cmd->sidemove = 400.0f;
                cmd->viewangles[1] -= 1.5f; // Smaller angle for more control
            }

            // Do not use forward movement for strafe hack
            cmd->forwardmove = 0.0f;
        }
    } else {
        // On ground - don't interfere with movement
        // This prevents the hack from affecting ground movement
        return;
    }

    // Prevent speed loss by limiting extreme movements
    if (velocity > 250.0f) {
        // At high speeds, make smaller adjustments to maintain momentum
        cmd->viewangles[1] = lastYaw + (cmd->viewangles[1] - lastYaw) * 0.5f;
    }
}

void Client::FastSwoop(float frametime, struct usercmd_s *cmd)
{
    // If fast swoop is disabled, return early
    if (!fastSwoopEnabled) {
        return;
    }

    // Check if W key is being pressed
    bool isForwardPressed = GetAsyncKeyState('W') & 0x8000;

    // Check if A or D keys are being pressed
    bool isMoveleftPressed = (cmd->buttons & IN_MOVELEFT) != 0;
    bool isMoverightPressed = (cmd->buttons & IN_MOVERIGHT) != 0;

    // Get current time
    float currentTime = g_Engine.GetClientTime();

    // Check if player is in bounce missile mode (movetype 11 for BOUNCEMISSILE)
    bool isInBounceMissile = g_Local.bAlive && g_Engine.GetLocalPlayer()->curstate.movetype == 11;

    // Just update the player position while in BOUNCEMISSILE mode
    if (isInBounceMissile) {
        // Update player position for collision detection
        if (currentTime - lastCollisionCheckTime >= 0.05f) {
            lastCollisionCheckTime = currentTime;
            lastPlayerPosition = g_Local.vOrigin;
        }
    }

    // Detect if bouncemissile was interrupted
    if (wasInBounceMissile && !isInBounceMissile && isForwardPressed) {
        // BOUNCEMISSILE mode was interrupted while still holding W
        bounceMissileInterrupted = true;
        g_Engine.Con_Printf("Swoop Debug: BOUNCEMISSILE interrupted while holding W\n");

        // Check for proximity to players at the moment of interruption
        Vector currentPlayerPosition = g_Local.vOrigin;
        CMe *cMe = g_cPlayers.Me();

        // Check distance to all players
        bool foundNearbyPlayer = false;
        int nearestPlayerIndex = -1;
        float nearestDistance = 9999.0f;

        for (int i = 1; i < 33; i++) {
            // Skip local player
            if (cMe->Entity()->index == i)
                continue;

            // Skip invalid entities
            cl_entity_s *playerEntity = g_Engine.GetEntityByIndex(i);
            if (!playerEntity || !playerEntity->player)
                continue;

            // Skip dead players
            if (!g_Player[i].bAlive)
                continue;

            // Calculate distance to this player
            Vector playerPos = playerEntity->origin;
            float distance = sqrt(POW(currentPlayerPosition[0] - playerPos[0]) +
                                 POW(currentPlayerPosition[1] - playerPos[1]) +
                                 POW(currentPlayerPosition[2] - playerPos[2]));

            // Check if this player is within range (100 units is close proximity)
            if (distance < 250.0f && distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayerIndex = i;
                foundNearbyPlayer = true;
            }
        }

        // If we found a nearby player, consider it a target collision
        if (foundNearbyPlayer) {
            targetCollisionDetected = true;
            g_Engine.Con_Printf("Swoop Debug: Target proximity detected! Near player %d at distance %.1f units\n",
                               nearestPlayerIndex, nearestDistance);
        }

        // Execute the reset sequence for interrupted bouncemissile only if a target collision was detected
        if (!swoopResetInProgress && targetCollisionDetected) {
            swoopResetInProgress = true;

            // Schedule the command to be executed after a delay
            g_Engine.Con_Printf("Swoop Reset: Scheduling interrupted bouncemissile reset sequence after target collision\n");

            // Set up the delayed execution
            swoopResetCommandPending = true;
            swoopResetCommandTime = currentTime + 0.3f;

            // Record the start time of the reset
            swoopResetStartTime = currentTime;

            // Reset the collision detection flag
            targetCollisionDetected = false;
        }
    }

    if (isInBounceMissile) {

        // Track how long W key has been pressed
        if (isForwardPressed) {
            // If this is the first frame W is pressed, record the start time
            if (!wasForwardPressed) {
                forwardPressStartTime = currentTime;
                wasForwardPressed = true;
            }

            // Calculate how long W has been pressed
            float forwardPressDuration = currentTime - forwardPressStartTime;

            // If W has been pressed for at least half a second and not already resetting
            if (forwardPressDuration >= 0.5f && !swoopResetInProgress) {
                // Debug output
                g_Engine.Con_Printf("Swoop Reset: W pressed for %.2f seconds, executing reset\n", forwardPressDuration);

                // Execute the swoop reset command sequence
                swoopResetInProgress = true;
                g_Engine.pfnClientCmd("-wwf; wait; wait; +wwf");

                // Record the start time of the reset
                swoopResetStartTime = currentTime;

                // Reset the timer
                forwardPressStartTime = currentTime;
            }

            // Debug output every second
            static float lastDebugTime = 0.0f;
            if (currentTime - lastDebugTime > 1.0f) {
                g_Engine.Con_Printf("Swoop Debug: W pressed for %.2f seconds, movetype=%d\n",
                    forwardPressDuration, g_Engine.GetLocalPlayer()->curstate.movetype);
                lastDebugTime = currentTime;
            }
        } else {
            // W key is not pressed, reset tracking
            wasForwardPressed = false;

        }

    }
    else {
        // Player is not in BOUNCEMISSILE mode - reset tracking variables
        lastVelocity = 0.0f;

        // Only reset wasForwardPressed if W is not being pressed
        if (!isForwardPressed) {
            wasForwardPressed = false;
            forwardPressStartTime = 0.0f;
        }
    }

    // Check if we have a pending command to execute
    if (swoopResetCommandPending && currentTime >= swoopResetCommandTime) {
        // Execute the command now that the delay has passed
        g_Engine.Con_Printf("Swoop Reset: Executing interrupted bouncemissile reset sequence\n");
        g_Engine.pfnClientCmd("-wwf; wait; wait; +wwf");

        // Mark the command as executed
        swoopResetCommandPending = false;
    }

    // Check if we need to reset the state variables based on elapsed time
    if (swoopResetInProgress) {
        float elapsedTime = currentTime - swoopResetStartTime;
        if (elapsedTime >= 0.5f) {
            // Reset the state after 0.5 seconds
            swoopResetInProgress = false;
            bounceMissileInterrupted = false;
            g_Engine.Con_Printf("Swoop Reset: State reset after %.2f seconds\n", elapsedTime);
        }
    }

    // Update the wasInBounceMissile state for the next frame
    wasInBounceMissile = isInBounceMissile;
}

// Removed weaponSettings function

void Client::CL_CreateMove(float frametime, struct usercmd_s *cmd, int active)
{
	g_Client.CL_CreateMove(frametime, cmd, active);

	g_Local.frametime = frametime;

	cl_entity_t *pLocal = g_Engine.GetLocalPlayer();
	g_Local.bAlive = pLocal && !(pLocal->curstate.effects & EF_NODRAW) && pLocal->player && pLocal->curstate.movetype !=6 && pLocal->curstate.movetype != 0;

	if (g_Local.bAlive && active && !Init && g_Local.bConnected)
	{
		bool bDoSmoothAim = false;
		if (CVAR(bhop))
		{
			Bhop(frametime, cmd);
		}

		if (CVAR(strafe_hack))
		{
			StrafeHack(frametime, cmd);
		}

		// Add the Fast Swoop feature call
		FastSwoop(frametime, cmd);

		// Update teleswoop state - check if teleswoop should be deactivated
		if (g_bTeleswoopActive) {
			float currentTime = g_Engine.GetClientTime();
			if (currentTime >= g_fTeleswoopEndTime) {
				g_bTeleswoopActive = false;
				g_Engine.Con_Printf("Teleswoop: Deactivated (sequence completed)\n");
			}
		}

		// Add the Perfect Block feature
		static bool perfectBlockActive = false;
		static bool perfectAimActive = false;
		if (g_bPerfectBlockActivatedByCommand && !g_bTeleswoopActive)
		{
			// Check if movement is NOT BOUNCEMISSILE (movetype != 11)
			bool isNotBounceMissile = (pLocal->curstate.movetype != 11);

			// Calculate max player velocity the same way goldsrc-monitor does it
			// Use both velocity and base velocity combined, like goldsrc-monitor
			Vector combinedVelocity;
			combinedVelocity[0] = g_Local.vVelocity[0] + g_Local.vBaseVelocity[0];
			combinedVelocity[1] = g_Local.vVelocity[1] + g_Local.vBaseVelocity[1];
			combinedVelocity[2] = g_Local.vVelocity[2] + g_Local.vBaseVelocity[2];

			// Use the 2D horizontal velocity calculation like goldsrc-monitor does
			// This matches goldsrc-monitor's: (GetVelocity() + GetBaseVelocity()).Length2D()
			float maxVelocity = sqrt(POW(combinedVelocity[0]) + POW(combinedVelocity[1]));

			// Check if max player velocity is not 0
			bool hasVelocity = (maxVelocity > 0.0f);

			// Handle +aim command - execute regardless of bouncemissile and velocity
			if (!perfectAimActive)
			{
				HlEngineCommand("+aim");
				perfectAimActive = true;
			}

			// Handle +block command - only when conditions are met
			if (isNotBounceMissile && hasVelocity)
			{
				if (!perfectBlockActive)
				{
					HlEngineCommand("+block");
					perfectBlockActive = true;
				}
			}
			else
			{
				if (perfectBlockActive)
				{
					HlEngineCommand("-block");
					perfectBlockActive = false;
				}
			}
		}
		else
		{
			// If perfect block is deactivated or teleswoop is active, make sure to clean up
			if (perfectBlockActive)
			{
				HlEngineCommand("-block");
				perfectBlockActive = false;
				if (g_bTeleswoopActive) {
					g_Engine.Con_Printf("Perfect Block: Disabled due to teleswoop activity\n");
				}
			}
			if (perfectAimActive && !g_bPerfectBlockActivatedByCommand)
			{
				HlEngineCommand("-aim");
				perfectAimActive = false;
			}
		}

		if (cvar.aim_triggerbot)
		{
			for(int i = 1;i < 33;i++)
				if ( g_Local.trigger[i] )
					cmd->buttons |= IN_ATTACK;
		}
		if (g_sAimbot.bActive)
		{
			bDoSmoothAim = true;
			if (cmd->buttons & IN_ATTACK)
			{
				if (!g_sAimbot.dwStartTime)
					g_sAimbot.dwStartTime = GetTickCount();
				DWORD dwNow = GetTickCount();
				if ((CVAR(aim_time) == 0.0f || (g_sAimbot.dwStartTime + CVAR(aim_time)) >= dwNow) &&
					(CVAR(aim_delay) == 0.0f || (g_sAimbot.dwStartTime + CVAR(aim_delay)) <= dwNow))
				{
					VectorCopy(g_cPlayers.Me()->Info()->vAimAngles, cmd->viewangles);
				}
				else
				{
					bDoSmoothAim = false;
				}
			}
			else
			{
				g_sAimbot.dwStartTime = 0;
			}
		}
		if (CVAR(aim_active) && CVAR(aim_smoothness))
		{
			if (bDoSmoothAim)
			{
				SmoothAimAngles(&(g_cPlayers.Me()->Info()->vPreAimAngles), &(g_cPlayers.Me()->Info()->vAimAngles), &(g_cPlayers.Me()->Info()->vPreAimAngles), CVAR(aim_smoothness));
				VectorCopy(g_cPlayers.Me()->Info()->vPreAimAngles, cmd->viewangles);
			}
			else
			{
				Vector vDelta;
				VectorSubtract(g_cPlayers.Me()->Info()->vPreAimAngles, g_cPlayers.Me()->Info()->vViewAngles, vDelta);
				if (vDelta[0] == 0.0f && vDelta[1] == 0.0f)
				{
					VectorCopy(g_cPlayers.Me()->Info()->vViewAngles, g_cPlayers.Me()->Info()->vPreAimAngles);
				}
				else
				{
					SmoothAimAngles(
						&(g_cPlayers.Me()->Info()->vPreAimAngles),
						&(cmd->viewangles),
						&(g_cPlayers.Me()->Info()->vPreAimAngles), CVAR(aim_smoothness));
					VectorCopy(g_cPlayers.Me()->Info()->vPreAimAngles, cmd->viewangles);
				}
			}
		}
	}
}

void Client::HookEngine(void)
{
	memcpy( &g_Engine, (LPVOID)g_pEngine, sizeof( cl_enginefunc_t ) );
}

void Client::HookStudio(void)
{
	memcpy( &g_Studio, (LPVOID)g_pStudio, sizeof( engine_studio_api_t ) );
	g_pStudio->StudioEntityLight = StudioEntityLight;
}
/*
int HUD_AddEntity(int type, struct cl_entity_s *ent, const char *modelname)
{
	int iRet = g_Client.HUD_AddEntity(type, ent, modelname);

	CPlayer *cPlayer;
	CMe *cMe = g_cPlayers.Me();
	cMe->GetInfo();

	for (int i = 1; i < 33; i++)
	{
		if (cMe->Entity()->index == i || cMe->Entity()->curstate.iuser2 == i)
			continue;

		cPlayer = g_cPlayers.Player(i);
		cPlayer->GetInfo(i);

		if (!cPlayer->Entity() || !cPlayer->Entity()->player)
			continue;

		BYTE r, g, b = 0;
		if (cPlayer->Info()->eTeam == TEAM_GOOD)
		{
			r = 255, g = 0, b = 0;
		}
		else if (cPlayer->Info()->eTeam == TEAM_EVIL)
		{
			r = 0, g = 0, b = 255;
		}
		else if (cPlayer->Info()->eTeam == TEAM_SPEC)
		{
			r = 255, g = 255, b = 0;
		}
		if (CVAR(esp_barel) && cPlayer->Info()->bValid && g_Player[i].bAlive)
		{
			Vector fwd;
			g_Engine.pfnAngleVectors(g_Player[i].Angles, fwd, 0, 0);
			VectorMul(fwd, 5000, fwd);
			fwd[0] = cPlayer->Info()->vAimBone.x + fwd[0];
			fwd[1] = cPlayer->Info()->vAimBone.y + fwd[1];
			fwd[2] = cPlayer->Info()->vAimBone.z + fwd[2];
			int beamindex = g_Engine.pEventAPI->EV_FindModelIndex("sprites/laserbeam.spr");
			fwd.z = -fwd.z;
			g_Engine.pEfxAPI->R_BeamPoints(cPlayer->Info()->vAimBone, fwd, beamindex,
				g_Local.frametime + 0.0001f, 1.0f, 0, 32, 0, 0, 0, r, g, b);
		}
	}

	return iRet;
}
*/
void Client::HookClient(void)
{
	memcpy( &g_Client, (LPVOID)g_pClient, sizeof( cl_clientfunc_t ) );
	g_pClient->HUD_Reset = HUD_Reset;
	g_pClient->HUD_Frame = HUD_Frame;
	g_pClient->HUD_Redraw = HUD_Redraw;
	g_pClient->HUD_PlayerMove = HUD_PlayerMove;
	g_pClient->CL_CreateMove = CL_CreateMove;
	g_pClient->V_CalcRefdef = V_CalcRefdef;
}
//HackMenu
bool isMenuVisible = true;
void Client::HackMenu()
{
	int mX = 10;
	int mY = 10;
	int mW = 182;
	int mH = 126; // Increased height to accommodate new option
	//border--
	int bX = mX - 1;
	int bY = mY - 1;
	int bW = mW + 2;
	int bH = mH + 2;
	int bS = 1;
	if(GetAsyncKeyState(VK_NUMPAD5)&1) {
		isMenuVisible = !isMenuVisible;
	}
	//Functions
	if(isMenuVisible) {
		if(GetAsyncKeyState(VK_UP)&1) {
			if(selectedItem2) { selectedItem2 = false; selectedItem1 = true; } else
			if(selectedItem3) { selectedItem3 = false; selectedItem2 = true; } else
			if(selectedItem6) { selectedItem6 = false; selectedItem3 = true; } else
			if(selectedItem7) { selectedItem7 = false; selectedItem6 = true; } else
			if(selectedItem8) { selectedItem8 = false; selectedItem7 = true; } else
			if(selectedItem9) { selectedItem9 = false; selectedItem8 = true; } else
			if(selectedItem1) { selectedItem1 = false; selectedItem9 = true; }
		}
		if(GetAsyncKeyState(VK_DOWN)&1) {
			if(selectedItem1) {
				selectedItem1 = false;
				selectedItem2 = true;
			} else if(selectedItem2) {
				selectedItem2 = false;
				selectedItem3 = true;
			} else if(selectedItem3) {
				selectedItem3 = false;
				selectedItem6 = true;
			} else if(selectedItem6) {
				selectedItem6 = false;
				selectedItem7 = true;
			} else if(selectedItem7) {
				selectedItem7 = false;
				selectedItem8 = true;
			} else if(selectedItem8) {
				selectedItem8 = false;
				selectedItem9 = true;
			} else if(selectedItem9) {
				selectedItem9 = false;
				selectedItem1 = true;
			}
		}
		if(GetAsyncKeyState(VK_RIGHT)&1) {
			if(selectedItem1) { activeItem1 = true; }
			if(selectedItem2) { activeItem2 = true; }
			if(selectedItem3) { activeItem3 = true; }
			if(selectedItem6) { activeItem6 = true; }
			if(selectedItem7) { activeItem7 = true; }
			if(selectedItem8) {
				activeItem8 = !activeItem8;
				cvar.strafe_hack = activeItem8 ? 1 : 0;
			}
			if(selectedItem9) {
				activeItem9 = !activeItem9;
				fastSwoopEnabled = activeItem9;
			}
		}
		if(GetAsyncKeyState(VK_LEFT)&1) {
			if(selectedItem1) { activeItem1 = false; }
			if(selectedItem2) { activeItem2 = false; }
			if(selectedItem3) { activeItem3 = false; }
			if(selectedItem6) { activeItem6 = false; }
			if(selectedItem7) { activeItem7 = false; }
			if(selectedItem8) {
				activeItem8 = false;
				cvar.strafe_hack = 0;
			}
			if(selectedItem9) {
				activeItem9 = false;
				fastSwoopEnabled = false;
			}
		}
		//<menubackground>
		g_Drawing.FillArea(mX,mY,mW,mH,0,0,0,110);
		g_Drawing.DrawString(mX + 10, mY + 7, 173, 216, 230, "        --RobinBot--");
		g_Drawing.DrawBox(bX,bY,bW,bH,bS,255,255,255,155);
		//</menubackground>
		//<espbox>
		if(selectedItem1) {
			g_Drawing.DrawString(mX + 10, mY + 25, 255, 165, 0, "ESP Box");
		} else {
			g_Drawing.DrawString(mX + 10, mY + 25, 255, 255, 255, "ESP Name");
		}
		if(activeItem1) {
			g_Drawing.DrawString(mX + 150, mY + 25, 0, 255, 0, " ON");
		} else {
			g_Drawing.DrawString(mX + 150, mY + 25, 255, 0, 0, "OFF");
		}
		//</espbox>
		//<espteam>
		if(selectedItem2) {
			g_Drawing.DrawString(mX + 10, mY + 38, 255, 165, 0, "ESP Team");
		} else {
			g_Drawing.DrawString(mX + 10, mY + 38, 255, 255, 255, "ESP Team");
		}
		if(activeItem2) {
			g_Drawing.DrawString(mX + 150, mY + 38, 0, 255, 0, " ON");
		} else {
			g_Drawing.DrawString(mX + 150, mY + 38, 255, 0, 0, "OFF");
		}
		//</espteam>
		//<aimbot>
		if(selectedItem3) {
			g_Drawing.DrawString(mX + 10, mY + 51, 255, 165, 0, "Aimbot");
		} else {
			g_Drawing.DrawString(mX + 10, mY + 51, 255, 255, 255, "Aimbot");
		}
		if(activeItem3) {
			g_Drawing.DrawString(mX + 135, mY + 51, 0, 255, 0, "SHIFT");
		} else {
			g_Drawing.DrawString(mX + 150, mY + 51, 255, 0, 0, "OFF");
		}
		//</aimbot>
		//<crosshair>
		if(selectedItem6) {
			g_Drawing.DrawString(mX + 10, mY + 64, 255, 165, 0, "Crosshair");
		} else {
			g_Drawing.DrawString(mX + 10, mY + 64, 255, 255, 255, "Crosshair");
		}
		if(activeItem6) {
			g_Drawing.DrawString(mX + 150, mY + 64, 0, 255, 0, " ON");
		} else {
			g_Drawing.DrawString(mX + 150, mY + 64, 255, 0, 0, "OFF");
		}
		//</crosshair>
		//<bunny>
		if(selectedItem7) {
			g_Drawing.DrawString(mX + 10, mY + 77, 255, 165, 0, "Bunny");
		} else {
			g_Drawing.DrawString(mX + 10, mY + 77, 255, 255, 255, "Bunny");
		}
		if(activeItem7) {
			g_Drawing.DrawString(mX + 150, mY + 77, 0, 255, 0, " ON");
		} else {
			g_Drawing.DrawString(mX + 150, mY + 77, 255, 0, 0, "OFF");
		}
		//</bunny>
		//<strafehack>
		if(selectedItem8) {
			g_Drawing.DrawString(mX + 10, mY + 90, 255, 165, 0, "Strafe Hack");
		} else {
			g_Drawing.DrawString(mX + 10, mY + 90, 255, 255, 255, "Strafe Hack");
		}
		if(activeItem8) {
			g_Drawing.DrawString(mX + 150, mY + 90, 0, 255, 0, " ON");
		} else {
			g_Drawing.DrawString(mX + 150, mY + 90, 255, 0, 0, "OFF");
		}
		//</strafehack>
		//<fastswoop>
		if(selectedItem9) {
			g_Drawing.DrawString(mX + 10, mY + 103, 255, 165, 0, "Fast Swoop");
		} else {
			g_Drawing.DrawString(mX + 10, mY + 103, 255, 255, 255, "Fast Swoop");
		}
		if(activeItem9) {
			g_Drawing.DrawString(mX + 150, mY + 103, 0, 255, 0, " ON");
		} else {
			g_Drawing.DrawString(mX + 150, mY + 103, 255, 0, 0, "OFF");
		}
		//</fastswoop>
	}
	CMe *cMe = g_cPlayers.Me();
	cMe->GetInfo();

	// Add speedmeter below crosshair (using goldsrc-monitor calculation)
	Vector combinedVel;
	combinedVel[0] = g_Local.vVelocity[0] + g_Local.vBaseVelocity[0];
	combinedVel[1] = g_Local.vVelocity[1] + g_Local.vBaseVelocity[1];
	float velocity = sqrt(POW(combinedVel[0]) + POW(combinedVel[1]));
	g_Drawing.DrawString(g_Screen.iWidth /2 - 30, g_Screen.iHeight /2 + 20, 0,255,255,"Speed: %.1f", velocity);

	// Display teleswoop status if active
	if (g_bTeleswoopActive) {
		float currentTime = g_Engine.GetClientTime();
		float remainingTime = g_fTeleswoopEndTime - currentTime;
		g_Drawing.DrawString(g_Screen.iWidth / 2 - 60, g_Screen.iHeight / 2 + 35, 255, 255, 0, "TELESWOOP ACTIVE (%.1fs)", remainingTime);
	}

	// Draw swoop reset timer if in BOUNCEMISSILE mode and W is pressed
	if (fastSwoopEnabled && wasForwardPressed && g_Local.bAlive &&
	    g_Engine.GetLocalPlayer()->curstate.movetype == 11) {
		float currentTime = g_Engine.GetClientTime();
		float forwardPressDuration = currentTime - forwardPressStartTime;
		float percentComplete = forwardPressDuration / 0.5f; // 0.5 seconds is our target

		if (percentComplete < 1.0f) {
			// Draw progress bar
			int barWidth = 100;
			int filledWidth = (int)(barWidth * percentComplete);
			int barHeight = 5;
			int barX = g_Screen.iWidth / 2 - barWidth / 2;
			int barY = g_Screen.iHeight / 2 + 35;

			// Draw background
			g_Drawing.FillArea(barX, barY, barWidth, barHeight, 100, 100, 100, 150);

			// Draw filled portion
			g_Drawing.FillArea(barX, barY, filledWidth, barHeight, 0, 255, 0, 200);

			// Draw text
			g_Drawing.DrawString(barX, barY - 10, 255, 255, 0, "Swoop Reset: %.1f%%", percentComplete * 100.0f);
		}
	}

	// Display bouncemissile interrupted status if active
	if (bounceMissileInterrupted && fastSwoopEnabled) {
		g_Drawing.DrawString(g_Screen.iWidth / 2 - 80, g_Screen.iHeight / 2 + 50, 255, 0, 0, "BOUNCEMISSILE INTERRUPTED - RESETTING");
	}

	if(activeItem6) {
		// Draw crosshair
		g_Engine.pfnFillRGBA(g_Screen.iWidth / 2 - 1, g_Screen.iHeight / 2 - 1, 3, 3, 255, 1, 1, 255);
	}
}

