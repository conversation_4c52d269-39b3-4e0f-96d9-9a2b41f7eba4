﻿  AutoOffset.cpp
  Client.cpp
  Command.cpp
  Main.cpp
\\192.168.0.31\RobinBot\RobinBot\Main.cpp(46,2): warning C4996: '_vsnprintf': This function or variable may be unsafe. Consider using _vsnprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  SetupHooks.cpp
  SvcMessage.cpp
  cvar.cpp
\\192.168.0.31\RobinBot\RobinBot\cvar.cpp(226,3): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  drawing.cpp
\\192.168.0.31\RobinBot\RobinBot\drawing.cpp(68,33): warning C4244: 'argumento': conversión de 'int' a 'float'; posible pérdida de datos
\\192.168.0.31\RobinBot\RobinBot\drawing.cpp(68,29): warning C4244: 'argumento': conversión de 'int' a 'float'; posible pérdida de datos
\\192.168.0.31\RobinBot\RobinBot\drawing.cpp(69,33): warning C4244: 'argumento': conversión de 'int' a 'float'; posible pérdida de datos
\\192.168.0.31\RobinBot\RobinBot\drawing.cpp(69,29): warning C4244: 'argumento': conversión de 'int' a 'float'; posible pérdida de datos
  interpreter.cpp
  players.cpp
  stringfinder.cpp
  usermsg.cpp
\\192.168.0.31\RobinBot\RobinBot\usermsg.cpp(15,2): warning C4996: '_strlwr': This function or variable may be unsafe. Consider using _strlwr_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  Generando código
  31 of 741 functions ( 4.2%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    18 functions had inline decision re-evaluated but remain unchanged
  Generación de código finalizada
  RobinBot.vcxproj -> \\192.168.0.31\RobinBot\RobinBot\Release\RobinBot.dll
